/* Professional Tours Catalog Styling */

/* Page-specific styling for tours-catalog */
.tours-catalog-page {
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  min-height: 100vh;
}

/* Hero Section Enhancements */
.tours-catalog-page .rich-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
}

.tours-catalog-page .rich-text::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.15"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.tours-catalog-page .rich-text .rich-text__wrapper {
  position: relative;
  z-index: 1;
}

.tours-catalog-page .rich-text h2 {
  font-size: 3.5rem;
  font-weight: 800;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.tours-catalog-page .rich-text .rich-text__text {
  font-size: 1.25rem;
  opacity: 0.95;
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Collection Section Styling */
.tours-catalog-page .collection {
  padding: 4rem 0;
  background: transparent;
}

.tours-catalog-page .collection__title {
  text-align: center;
  margin-bottom: 4rem;
}

.tours-catalog-page .collection__title .title {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #1f2937, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

/* Professional Grid Layout */
.tours-catalog-page .grid {
  gap: 2.5rem;
  padding: 0 1rem;
  max-width: 1400px;
  margin: 0 auto;
}

.tours-catalog-page .grid__item {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Enhanced Card Styling */
.tours-catalog-page .card-wrapper {
  height: 100%;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tours-catalog-page .card {
  height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  overflow: hidden;
  background: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.04);
  position: relative;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.tours-catalog-page .card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #06b6d4, #10b981);
  z-index: 1;
}

.tours-catalog-page .card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

/* Image Styling */
.tours-catalog-page .card__media {
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  height: 280px;
}

.tours-catalog-page .card__media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.tours-catalog-page .card:hover .card__media img {
  transform: scale(1.08);
}

/* Content Area */
.tours-catalog-page .card__content {
  padding: 2rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.tours-catalog-page .card__heading {
  font-size: 1.375rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 1rem;
  line-height: 1.3;
  flex-shrink: 0;
}

.tours-catalog-page .card__heading a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

.tours-catalog-page .card__heading a:hover {
  color: #3b82f6;
}

/* Professional Rating System */
.tours-catalog-page .card__professional-reviews {
  margin: 1.25rem 0;
  flex-shrink: 0;
}

.tours-catalog-page .rating--professional {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.tours-catalog-page .rating--professional .rating-star {
  color: #fbbf24;
  font-size: 1.25rem;
}

.tours-catalog-page .rating--professional + .rating-text {
  font-size: 0.9375rem;
  color: #6b7280;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tours-catalog-page .rating-count {
  color: #9ca3af;
  font-size: 0.875rem;
}

/* Enhanced Price Display */
.tours-catalog-page .card__price-section {
  margin: 1.5rem 0;
  padding: 1rem 0;
  border-top: 2px solid #f3f4f6;
  border-bottom: 2px solid #f3f4f6;
  flex-shrink: 0;
}

.tours-catalog-page .price {
  font-size: 1.75rem;
  font-weight: 800;
  background: linear-gradient(135deg, #059669, #10b981);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tours-catalog-page .price--compare-at {
  font-size: 1.125rem;
  color: #9ca3af;
  text-decoration: line-through;
  margin-left: 0.75rem;
}

/* Professional CTA Button */
.tours-catalog-page .card__view-details {
  margin-top: auto;
  padding-top: 1.5rem;
}

.tours-catalog-page .button--view-details {
  width: 100%;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  border: none;
  padding: 1rem 1.5rem;
  border-radius: 16px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.tours-catalog-page .button--view-details::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.tours-catalog-page .button--view-details:hover::before {
  left: 100%;
}

.tours-catalog-page .button--view-details:hover {
  background: linear-gradient(135deg, #2563eb, #7c3aed);
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
}

.tours-catalog-page .button--view-details svg {
  width: 18px;
  height: 18px;
  transition: transform 0.3s ease;
}

.tours-catalog-page .button--view-details:hover svg {
  transform: translateX(6px);
}

/* Badge Styling */
.tours-catalog-page .badge {
  border-radius: 24px;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  backdrop-filter: blur(10px);
}

.tours-catalog-page .badge--bottom-left {
  position: absolute;
  bottom: 1.5rem;
  left: 1.5rem;
  z-index: 2;
  background: rgba(0, 0, 0, 0.7);
  color: white;
}

/* Mobile Responsive Design */
@media (max-width: 768px) {
  .tours-catalog-page .rich-text h2 {
    font-size: 2.5rem;
  }
  
  .tours-catalog-page .rich-text .rich-text__text {
    font-size: 1.125rem;
  }
  
  .tours-catalog-page .collection__title .title {
    font-size: 2rem;
  }
  
  .tours-catalog-page .grid {
    gap: 1.5rem;
    padding: 0 0.75rem;
  }
  
  .tours-catalog-page .card__media {
    height: 220px;
  }
  
  .tours-catalog-page .card__content {
    padding: 1.5rem;
  }
  
  .tours-catalog-page .card__heading {
    font-size: 1.25rem;
  }
  
  .tours-catalog-page .price {
    font-size: 1.5rem;
  }
  
  .tours-catalog-page .card:hover {
    transform: translateY(-8px) scale(1.01);
  }
}

@media (max-width: 480px) {
  .tours-catalog-page .rich-text {
    padding: 3rem 0;
  }
  
  .tours-catalog-page .rich-text h2 {
    font-size: 2rem;
  }
  
  .tours-catalog-page .collection__title .title {
    font-size: 1.75rem;
  }
  
  .tours-catalog-page .card__media {
    height: 200px;
  }
  
  .tours-catalog-page .card__content {
    padding: 1.25rem;
  }
}
